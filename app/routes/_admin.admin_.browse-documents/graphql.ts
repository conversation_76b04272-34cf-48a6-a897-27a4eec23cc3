import { graphql } from '~/gql'

export const GET_DOCUMENTS = graphql(`
  query GetDocuments(
    $first: Int!
    $page: Int
    $added_date: Date
    $baptisma_filter: BaptismaRecordFilterInput
    $inneih_filter: InneihRecordFilterInput
    $others_filter: OtherRecordInput
  ) {
    getDocuments(
      first: $first
      page: $page
      added_date: $added_date
      baptisma_filter: $baptisma_filter
      inneih_filter: $inneih_filter
      others_filter: $others_filter
    ) {
      data {
        id
        title
        body
        tags
        added_date
        category_id
        is_classified
        extra_record {
          __typename
          ... on InneihRecord {
            id
            inneih_registration_no: registration_no
            mipa_hming
            mipa_pa_hming
            mipa_khua
            hmeichhe_hming
            hmeichhe_pa_hming
            hmeichhe_khua
            inneih_ni
            hmun
            inneihtirtu
          }
          ... on BaptismaRecord {
            id
            baptisma_registration_no: registration_no
            hming
            pa_hming
            nu_hming
            pian_ni
            pian_ni_remarks
            baptisma_chan_ni
            baptisma_chan_ni_remarks
            khua
            chantirtu
          }
        }
        files {
          id
          doc_id
          path
        }
      }
      paginator_info {
        last_page
      }
    }
  }
`)

export const UPDATE_RECORD = graphql(`
  mutation UpdateRecord(
    $inneih_record: InneihRecordUpdateInput
    $baptisma_record: BaptismaRecordUpdateInput
    $document: DocumentUpdateInput!
  ) {
    updateRecord(
      inneih_record: $inneih_record
      baptisma_record: $baptisma_record
      document: $document
    )
  }
`)

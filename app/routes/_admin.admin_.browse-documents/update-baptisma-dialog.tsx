import type { UpdateRecordType } from './schema'
import type { GetDocumentsQuery } from '~/gql/graphql'
import { format } from 'date-fns'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { useAppForm } from '~/hooks/form'
import useUpdateDocuments from './use-update-documents'

type BaptismaRecordFromQuery = NonNullable<NonNullable<GetDocumentsQuery['getDocuments']['data']>[0]['extra_record']> & { __typename: 'BaptismaRecord' }
type DocumentFromQuery = NonNullable<GetDocumentsQuery['getDocuments']['data']>[0]

interface Props {
  isOpen: boolean
  toggle: (open: boolean) => void
  record: BaptismaRecordFromQuery
  _document: DocumentFromQuery
}

export default function UpdateBaptismaDialog({ isOpen, toggle, record, _document }: Props) {
  const { updateBaptismaRecord } = useUpdateDocuments()

  const form = useAppForm({
    defaultValues: {
      baptisma_record: {
        id: record.id,
        registration_no: record.baptisma_registration_no || '',
        hming: record.hming || '',
        pa_hming: record.pa_hming || '',
        nu_hming: record.nu_hming || '',
        khua: record.khua || '',
        pian_ni: record.pian_ni ? format(new Date(record.pian_ni), 'yyyy-MM-dd') : '',
        pian_ni_remarks: record.pian_ni_remarks || '',
        baptisma_chan_ni: record.baptisma_chan_ni ? format(new Date(record.baptisma_chan_ni), 'yyyy-MM-dd') : '',
        baptisma_chan_ni_remarks: record.baptisma_chan_ni_remarks || '',
        chantirtu: record.chantirtu || '',
      },
      document: {
        id: _document.id,
        title: _document.title || '',
        body: _document.body || '',
        tags: _document.tags || '',
        is_classified: _document.is_classified || false,
        category_id: _document.category_id || '',
        added_date: _document.added_date ? format(new Date(_document.added_date), 'yyyy-MM-dd') : '',
        files: undefined as File[] | undefined,
      },
    } as UpdateRecordType,
    onSubmit: async ({ value}) => {
      await updateBaptismaRecord.mutateAsync(value, {
        onSuccess: () => {
          toggle(false)
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogContent className="w-full min-w-256">
        <DialogHeader>
          <DialogTitle>
            Update Baptisma Record
          </DialogTitle>
          <DialogDescription>
            Enter update details
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="flex flex-col gap-4"
        >
          <div className="flex gap-x-8">
            <div className="w-full">
              <form.AppField
                name="baptisma_record.hming"
                children={field => <field.InputField label="Hming" />}
              />
            </div>
            <div className="w-full">
              <form.AppField
                name="baptisma_record.khua"
                children={field => <field.InputField label="Khua" />}
              />
            </div>
          </div>
          <div className="flex gap-x-8">
            <div className="w-full">
              <form.AppField
                name="baptisma_record.pa_hming"
                children={field => <field.InputField label="Pa hming" />}
              />
            </div>
            <div className="w-full">
              <form.AppField
                name="baptisma_record.nu_hming"
                children={field => <field.InputField label="Nu hming" />}
              />
            </div>
          </div>
          <div className="flex gap-x-8">
            <div className="w-full">
              <form.AppField
                name="baptisma_record.pian_ni"
                children={field => <field.InputField label="Pian ni" type="date" />}
              />
            </div>
            <div className="w-full">
              <form.AppField
                name="baptisma_record.baptisma_chan_ni"
                children={field => <field.InputField label="Baptisma Chan ni" type="date" />}
              />
            </div>
          </div>
          <div className="flex gap-x-8">
            <div className="w-full">
              <form.AppField
                name="baptisma_record.pian_ni_remarks"
                children={field => <field.TextareaField label="Pian ni remarks" />}
              />
            </div>
            <div className="w-full">
              <form.AppField
                name="baptisma_record.baptisma_chan_ni_remarks"
                children={field => <field.TextareaField label="Baptisma chan ni remarks" />}
              />
            </div>
          </div>
          <div className="flex gap-x-8">
            <div className="basis-1/2">
              <form.AppField
                name="baptisma_record.chantirtu"
                children={field => <field.InputField label="Baptisma chantirtu" />}
              />
            </div>
            <div className="basis-1/2">
              <form.AppField
                name="baptisma_record.registration_no"
                children={field => <field.InputField label="Registration No" />}
              />
            </div>
          </div>
          <h3 className="text-lg font-semibold">Document Information</h3>
          <div className="flex gap-x-4">
            <div className="w-full">
              <form.AppField
                name="document.title"
                children={field => <field.InputField label="Title" />}
              />
            </div>
            <div className="w-full">
              <form.AppField
                name="document.tags"
                children={field => <field.PillInput label="Tags" />}
              />
            </div>
          </div>
          <div className="w-full">
            <form.AppField
              name="document.body"
              children={field => <field.InputRichText label="Body" />}
            />
          </div>
          <div className="flex gap-x-4">
            <div className="w-full">
              <form.Field
                name="document.files"
                children={field => (
                  <Label className="flex flex-col items-start gap-y-2">
                    <div>Upload file</div>
                    <Input
                      type="file"
                      multiple={true}
                      accept="image/*,application/pdf"
                      onChange={(e) => {
                        if (e.target.files && e.target.files.length > 0) {
                          field.handleChange(Array.from(e.target.files))
                        }
                        else {
                          field.handleChange([])
                        }
                      }}
                      className="bg-white"
                    />

                  </Label>
                )}
              />
            </div>
            <div className="w-full">
              <form.AppField
                name="document.added_date"
                children={field => <field.InputField type="date" label="Added date" />}
              />
            </div>
          </div>
          <div>
            <form.AppField
              name="document.is_classified"
              children={field => <field.CheckboxField label="Classified" />}
            />
          </div>
          <DialogFooter>
            <form.AppForm>
              <form.SubmitButton label="Update" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
